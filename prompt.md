# 初始提示词
你是一名ai提示词专家，请帮我优化、完善下面的提示词。

我要基于qwen3大模型，训练一个面向宠物医疗领域的大模型，该大模型能同时支持亚洲主要国家和地区的语言。
请帮我按照下面的背景和要求，设计一个完整的训练方案。
## 现有的语料
- 10万中文的结构化宠物电子病历
- 500本英文宠物诊疗书籍
- 1000个英文科学文献论文

## 要求
- 主要基于上面提供的现有的语料
- 推荐其它的开源的数据集、语料、图书等
- 设计方案中，使用文字描述、表格、mermaid图表等格式，不要代码
- 对于模型的每个阶段的训练，请详细说明训练的目的和目标、使用的数据、训练方法、推荐的参数、需要的服务器尤其是显卡资源、训练时长等
- 可以调用商用大模型例如Claude4来进行数据合成、模型蒸馏等

# 宠物医疗多语言大模型训练方案设计

## 项目背景
您需要基于Qwen3大模型，训练一个专门面向宠物医疗领域的多语言大模型，支持亚洲主要国家和地区的语言（中文、日文、韩文、泰文、越南文、马来文、印尼文等）。

## 现有数据资源
| 数据类型 | 数量 | 语言 | 数据质量评估 |
|---------|------|------|-------------|
| 结构化宠物电子病历 | 10万条 | 中文 | 高质量，包含症状、诊断、治疗方案 |
| 宠物诊疗书籍 | 500本 | 英文 | 权威性强，理论知识丰富 |
| 科学文献论文 | 1000篇 | 英文 | 前沿研究，循证医学证据 |

## 目标语言覆盖范围
- **主要语言**：中文（简体/繁体）、英文、日文、韩文
- **次要语言**：泰文、越南文、马来文、印尼文
- **语言能力要求**：理解、生成、翻译、跨语言推理

## 详细训练方案要求

请先写一个训练的整体方案，然后再为每个阶段提供详细信息。
请为每个训练阶段提供以下详细信息：

### 1. 训练目标与数据规划
- **阶段目标**：明确说明该阶段要达到的具体能力
- **数据来源**：详细列出使用的数据集和预处理方法
- **数据增强**：说明如何利用Claude-4等商用模型进行数据合成和扩充

### 2. 技术实现细节
- **训练方法**：具体的训练策略（预训练/微调/强化学习等）
- **模型架构调整**：针对多语言和医疗领域的特殊设计
- **损失函数设计**：适合医疗领域的评估指标

### 3. 资源配置与时间规划
- **硬件需求**：
  - GPU型号和数量（如A100/H100）
  - 内存和存储要求
  - 网络带宽需求
- **训练时长**：每个阶段的预估训练时间
- **成本估算**：云服务器或自建服务器的成本分析

### 4. 数据扩充建议
请推荐以下类型的开源资源：
- **多语言医疗数据集**
- **宠物医疗相关的开源语料库**
- **医学术语词典和知识图谱**
- **可用于数据合成的提示词模板**

### 5. 质量控制与评估
- **训练过程监控指标**
- **模型性能评估方法**
- **多语言能力测试基准**
- **医疗专业知识准确性验证**

## 输出格式要求
- 使用清晰的文字描述，避免代码
- 用表格整理关键信息和参数配置
- 用Mermaid流程图展示训练流程和数据流向
- 提供具体的时间线和里程碑规划

## 特殊考虑因素
- **数据隐私**：医疗数据的脱敏和合规处理
- **模型安全**：避免生成有害的医疗建议
- **多语言平衡**：确保各语言的性能均衡
- **领域适应性**：保持通用能力的同时增强专业能力

请基于以上要求，设计一个完整、可执行的训练方案。
